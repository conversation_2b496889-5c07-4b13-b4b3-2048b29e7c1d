{"question1": {"title": "How to properly handle async data fetching in React?", "text": "I'm building a React application that fetches data from an API, but I'm running into issues with state management. Sometimes I get errors about updating state on unmounted components, and other times the data doesn't load properly. Here's my current component code:\n\n```javascript\nfunction DataComponent() {\n  const [data, setData] = useState(null);\n  \n  useEffect(() => {\n    api.getData().then(response => {\n      setData(response);\n    }).catch(err => {\n      console.error(err);\n    });\n  }, []);\n  \n  return (\n    <div>\n      {data ? renderData(data) : <Loading />}\n    </div>\n  );\n}\n```\n\nWhat am I doing wrong and how can I fix this pattern?", "tags": ["tag1", "tag3", "tag5"], "answers": ["answer1"], "askedBy": "newbiereactdev", "askDateTime": "2025-03-15T07:42:18Z", "views": ["user123", "user456", "user789", "user101", "user202"], "upVotes": ["user123", "user456"], "downVotes": [], "comments": ["comment1"], "community": "community1"}, "question2": {"title": "Node.js memory issues when handling large file uploads", "text": "I have a Node.js application that allows users to upload files. When dealing with large files (>100MB), the server sometimes crashes with an 'out of memory' error. Here's my current upload handling code:\n\n```javascript\napp.post('/upload', (req, res) => {\n  const form = new formidable.IncomingForm();\n  \n  form.parse(req, (err, fields, files) => {\n    if (err) {\n      return res.status(500).json({ error: err.message });\n    }\n    \n    const fileData = fs.readFileSync(files.uploadFile.path);\n    // Process file data\n    // Save to database or disk\n    \n    res.status(200).json({ message: 'Upload successful' });\n  });\n});\n```\n\nHow can I optimize this to handle large files without running out of memory?", "tags": ["tag2", "tag7"], "answers": ["answer2"], "askedBy": "serverdev", "askDateTime": "2025-03-18T11:19:44Z", "views": ["user123", "user234", "user345", "user456", "user567", "user678", "user789"], "upVotes": ["user234", "user345", "user456"], "downVotes": [], "comments": ["comment5"], "community": null}, "question3": {"title": "Webpack configuration issues with latest JavaScript features", "text": "I'm trying to use the latest JavaScript features (like optional chaining and nullish coalescing) in my React project, but I keep getting syntax errors when webpack tries to build the application. I've updated my babel configuration but it's still not working.\n\nHere's my current webpack.config.js:\n\n```javascript\nmodule.exports = {\n  entry: './src/index.js',\n  output: {\n    path: path.resolve(__dirname, 'dist'),\n    filename: 'bundle.js'\n  },\n  module: {\n    rules: [\n      {\n        test: /\\.js$/,\n        exclude: /node_modules/,\n        use: 'babel-loader'\n      }\n    ]\n  }\n};\n```\n\nAnd my .babelrc:\n\n```json\n{\n  \"presets\": [\"@babel/preset-env\", \"@babel/preset-react\"]\n}\n```\n\nWhat am I missing in my configuration?", "tags": ["tag4", "tag8"], "answers": ["answer3"], "askedBy": "webpacknewbie", "askDateTime": "2025-03-20T08:36:51Z", "views": ["user123", "user234", "user345", "user456"], "upVotes": ["user234"], "downVotes": ["user345"], "comments": ["comment3", "comment6"], "community": null}, "question4": {"title": "Optimizing a slow PostgreSQL query with JOINs", "text": "I have a PostgreSQL query that's running very slowly in production. It's a relatively simple query with a JOIN, but it takes over 10 seconds to execute even though the tables aren't that large (around 100k rows each).\n\n```sql\nSELECT u.id, u.email, u.full_name, p.bio\nFROM users u\nLEFT JOIN profiles p ON u.id = p.user_id\nWHERE u.status = 'active'\n  AND u.created_at > '2025-01-01'\nORDER BY u.created_at DESC\nLIMIT 100;\n```\n\nI've run EXPLAIN ANALYZE and it shows a sequential scan is being used. How can I optimize this query for better performance?", "tags": ["tag6", "tag9"], "answers": ["answer4"], "askedBy": "dbnovice", "askDateTime": "2025-03-30T15:21:38Z", "views": ["user123", "user234", "user345", "user456", "user567", "user678", "user789", "user890"], "upVotes": ["user234", "user345", "user456", "user567"], "downVotes": [], "comments": ["comment7"], "community": "community2"}, "question5": {"title": "How to handle edge cases in JavaScript array processing", "text": "I've written a function to process arrays of data, but it breaks when it encounters certain edge cases. Here's my current implementation:\n\n```javascript\nfunction processArray(arr) {\n  const results = [];\n  \n  for (let i = 0; i < arr.length; i++) {\n    const processed = doSomethingWith(arr[i]);\n    results.push(processed);\n  }\n  \n  return results;\n}\n```\n\nThis function breaks when the input is null, not an array, or contains null items. What's the best way to make this function robust against these edge cases?", "tags": ["tag1", "tag8"], "answers": ["answer5"], "askedBy": "jsdeveloper", "askDateTime": "2025-04-02T09:18:27Z", "views": ["user123", "user234", "user345"], "upVotes": ["user123"], "downVotes": [], "comments": ["comment8"], "community": null}, "question6": {"title": "Fixing CORS issues with fetch API in React frontend", "text": "I'm building a React application that needs to make API calls to a separate backend server. I'm using the fetch API, but I keep getting CORS errors in the console:\n\n```\nAccess to fetch at 'https://api.mybackend.com/data' from origin 'https://myapp.com' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.\n```\n\nHere's my fetch code:\n\n```javascript\nfetch('https://api.mybackend.com/data', {\n  method: 'GET',\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n.then(response => response.json())\n.then(data => console.log(data))\n.catch(error => console.error('Error:', error));\n```\n\nHow can I resolve these CORS issues?", "tags": ["tag3", "tag10"], "answers": ["answer6"], "askedBy": "frontendne<PERSON><PERSON>", "askDateTime": "2025-04-05T08:15:44Z", "views": ["user123", "user234", "user345", "user456", "user567"], "upVotes": ["user234", "user345"], "downVotes": [], "comments": ["comment9"], "community": null}, "question7": {"title": "Improving performance of Python data processing script", "text": "I have a Python script that processes large datasets, but it's running very slowly. I'm using basic loops to iterate through the data and perform calculations. Here's a simplified version of my code:\n\n```python\ndef process_data(data, threshold):\n    results = []\n    for item in data:\n        if item > threshold:\n            processed = item * 1.5\n        else:\n            processed = item / 2\n        results.append(processed)\n    \n    # Calculate statistics\n    mean_val = sum(results) / len(results)\n    \n    # More processing...\n    return results, mean_val\n\n# Using the function\ndata = [random.random() * 100 for _ in range(1000000)]\nresults, mean = process_data(data, 50)\n```\n\nThis takes several minutes to run with my large dataset. How can I optimize it?", "tags": ["tag2", "tag5"], "answers": ["answer7"], "askedBy": "pythonuser", "askDateTime": "2025-04-08T13:47:22Z", "views": ["user123", "user234", "user345", "user456"], "upVotes": ["user123", "user234"], "downVotes": [], "comments": ["comment5"], "community": "community3"}, "question8": {"title": "Docker container environment variable configuration", "text": "I'm trying to set up a Docker container for my Node.js application, but I'm having trouble with environment variables. The application needs different environment variables for development and production, and I'm not sure how to configure this properly.\n\nHere's my current Dockerfile:\n\n```dockerfile\nFROM node:14\n\nWORKDIR /app\n\nCOPY package*.json ./\nRUN npm install\n\nCOPY . .\n\nENV NODE_ENV=production\nENV PORT=3000\n\nEXPOSE 3000\n\nCMD [\"node\", \"server.js\"]\n```\n\nAnd I'm running it with:\n\n```bash\ndocker run -p 3000:3000 myapp\n```\n\nBut I need different configurations for different environments. What's the best practice for handling environment variables in Docker?", "tags": ["tag4", "tag7"], "answers": ["answer8"], "askedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "askDateTime": "2025-04-10T10:22:36Z", "views": ["user123", "user234", "user345", "user456", "user567", "user678"], "upVotes": ["user234", "user345", "user456"], "downVotes": [], "comments": [], "community": "community4"}, "question9": {"title": "Proper way to handle async/await in JavaScript", "text": "I'm trying to use async/await in my JavaScript code, but I'm still getting Promise-related errors. Here's my current implementation:\n\n```javascript\nfunction fetchUserData(userId) {\n  return async function() {\n    const response = fetch(`https://api.example.com/users/${userId}`);\n    const data = response.json();\n    return data;\n  }\n}\n\nconst getUserData = fetchUserData(123);\ngetUserData().then(data => {\n  console.log(data);\n});\n```\n\nBut I'm getting errors about promises not being resolved properly. What am I doing wrong with async/await here?", "tags": ["tag1", "tag10"], "answers": ["answer9"], "askedBy": "promisenovice", "askDateTime": "2025-04-14T09:38:52Z", "views": ["user123", "user234"], "upVotes": ["user123"], "downVotes": [], "comments": ["comment1"], "community": "community1"}, "question10": {"title": "Preventing memory leaks in React applications", "text": "I've noticed that my React application is consuming more and more memory the longer it runs. I suspect I have memory leaks, particularly when components are mounting and unmounting frequently. My suspicion is that I'm not cleaning up subscriptions or event listeners properly.\n\nHere's a simplified version of one of my components:\n\n```javascript\nfunction DataMonitor() {\n  const [data, setData] = useState([]);\n  \n  useEffect(() => {\n    // Subscribe to data source\n    dataSource.subscribe(newData => {\n      setData(prevData => [...prevData, newData]);\n    });\n    \n    // Add window event listener\n    window.addEventListener('resize', handleResize);\n  }, []);\n  \n  function handleResize() {\n    console.log('Window resized');\n  }\n  \n  return <div>{/* Render data */}</div>;\n}\n```\n\nWhat specific changes do I need to make to prevent memory leaks?", "tags": ["tag3", "tag5"], "answers": ["answer10"], "askedBy": "reactlearner", "askDateTime": "2025-04-17T11:27:09Z", "views": ["user123", "user234", "user345"], "upVotes": ["user123", "user234"], "downVotes": [], "comments": ["comment2"], "community": "community1"}}