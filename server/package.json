{"name": "@fake-stack-overflow/server", "version": "1.0.0", "dependencies": {"@fake-stack-overflow/shared": "^1.0.0", "dotenv": "^16.4.7", "express": "^4.21.2", "mongodb": "^6.15.0", "mongoose": "8.9.2", "nanoid": "^3.3.8", "socket.io": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.24.0", "@stryker-mutator/core": "^8.7.1", "@stryker-mutator/jest-runner": "^8.7.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@types/supertest": "^6.0.2", "@typescript-eslint/parser": "^8.29.0", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "jest": "^29.7.0", "mockingoose": "^2.16.2", "nodemon": "^3.1.9", "prettier": "^3.5.3", "socket.io-client": "^4.8.1", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.29.0"}, "scripts": {"dev": "nodemon server.ts", "build": "tsc", "lint": "eslint .", "lint:fix": "eslint . --fix", "start": "MODE=production node ./dist/server.js", "test": "jest -w=1 --coverage --detectOpenHandles --coverageReporters='json-summary'", "debug-test": "jest -w=1 --coverage --detect<PERSON><PERSON>Handles", "prebuild": "cd ..; npm run build --workspace=shared", "stryker": "stryker run", "populate-db": "ts-node ./seedData/populateDB.ts", "delete-db": "ts-node ./seedData/deleteDB.ts"}}