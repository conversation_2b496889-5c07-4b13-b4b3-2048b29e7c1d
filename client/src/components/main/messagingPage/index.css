* {
  box-sizing: border-box;
}

.chat-room {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  margin: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chat-header {
  background-color: #3090e2;
  color: white;
  padding: 10px 20px;
  text-align: center;
}

.chat-messages {
  flex: 1;
  padding: 5px;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #f9f9f9;
}

.user-name {
  font-weight: bold;
  margin-right: 5px;
}

.message-input {
  display: flex;
  padding: 10px;
  border-top: 1px solid #ddd;
  background-color: #fff;
}

.message-textbox {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-right: 10px;
  width: 100%;
  resize: none;
}

.message-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.send-button {
  padding: 10px 20px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  width: 100px;
}

.send-button:hover {
  background-color: #0056b3;
}

.error-message {
  color: red;
  font-size: 0.85rem;
  margin-left: 10px; /* Adds space between the button and the error message */
  background-color: #ffe6e6; /* Light red background for better visibility */
  padding: 5px 10px;
  border: 1px solid #ffcccc;
  border-radius: 4px;
  display: inline-block; /* Prevents it from stretching unnecessarily */
  white-space: nowrap; /* Prevents wrapping of the message */
}
