import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { createGame, getGames } from '../services/gamesService';
import { GameInstance, GameState, GameType } from '../types/types';

/**
 * Custom hook to manage the state and logic for the "All Collections" page, including fetching collections,
 * creating a new collection, and navigating to collection details.
 * @returns An object containing the following:
 * - `availableCollections`: The list of available game instances.
 * - `fetchCollections`: A function to fetch the list of available collections.
 * - `isCreateCollectionModalOpen`: A boolean indicating whether the collectino creation modal is open.
 * - `handleToggleCreateCollectionModal`: A function to toggle the visibility of the collection creation modal.
 */
const useAllCollectionsPage = (username: string) => {
  const navigate = useNavigate();
  const [collections, setCollections] = useState([]);
  const [isCreateCollectionModalOpen, setIsCreateCollectionModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCollections = async () => {
    try {
        setLoading(true);
      const data = await getAllCollectionsByUsername(username);
      setCollections(data);
    } catch (err as Error) {
      setError(err.message);
    }
  };

  const handleCreateGame = async (gameType: GameType) => {
    try {
      await createGame(gameType);
      await fetchGames(); // Refresh the list after creating a game
    } catch (createGameError) {
      setError('Error creating game');
    }
  };

  const handleJoin = (gameID: string) => {
    navigate(`/games/${gameID}`);
  };

  useEffect(() => {
    fetchGames();
  }, []);

  const handleToggleModal = () => {
    setIsModalOpen(prevState => !prevState);
  };

  const handleSelectGameType = (gameType: GameType) => {
    handleCreateGame(gameType);
    handleToggleModal();
  };

  return {
    availableGames,
    handleJoin,
    fetchGames,
    isModalOpen,
    handleToggleModal,
    handleSelectGameType,
    error,
  };
};

export default useAllCollectionsPage;
