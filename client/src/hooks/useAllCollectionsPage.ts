import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { getCollectionsByUsername } from '../services/collectionService';
import { PopulatedDatabaseCollection } from '../types/types';
import useUserContext from './useUserContext';

/**
 * Custom hook to manage the state and logic for the "All Collections" page, including fetching collections,
 * creating a new collection, and navigating to collection details.
 * @param username - The username of the user whose collections to fetch
 * @returns An object containing the following:
 * - `collections`: The list of available collections.
 * - `fetchCollections`: A function to fetch the list of available collections.
 * - `isCreateCollectionModalOpen`: A boolean indicating whether the collection creation modal is open.
 * - `handleToggleCreateCollectionModal`: A function to toggle the visibility of the collection creation modal.
 * - `handleNavigateToCollection`: A function to navigate to a specific collection.
 * - `loading`: A boolean indicating if data is being fetched.
 * - `error`: An error message if something went wrong.
 */
const useAllCollectionsPage = (username: string) => {
  const navigate = useNavigate();
  const { user } = useUserContext();
  const currentUsername = user.username;
  const [collections, setCollections] = useState<PopulatedDatabaseCollection[]>([]);
  //   const [isCreateCollectionModalOpen, setIsCreateCollectionModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCollections = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getCollectionsByUsername(, currentUsername);
      setCollections(data);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setLoading(false);
    }
  }, [user, ]);

  //   const handleToggleCreateCollectionModal = () => {
  //     setIsCreateCollectionModalOpen(prevState => !prevState);
  //   };

  const handleNavigateToCollection = (collectionId: string) => {
    navigate(`/collections/${collectionId}`);
  };

  useEffect(() => {
    fetchCollections();
  }, [fetchCollections]);

  return {
    collections,
    fetchCollections,
    // isCreateCollectionModalOpen,
    // handleToggleCreateCollectionModal,
    handleNavigateToCollection,
    loading,
    error,
  };
};

export default useAllCollectionsPage;
