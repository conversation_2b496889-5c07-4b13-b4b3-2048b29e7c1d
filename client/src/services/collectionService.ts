import { Collection, PopulatedDatabaseCollection } from '../types/types';
import api from './config';

const COLLECTION_API_URL = `/api/collection`;

/**
 * Fetches all collections for a specific user.
 *
 * @param usernameToView - The username of the user whose collections are to be fetched.
 * @param currentUsername - The username of the current user (for privacy filtering).
 * @returns The list of collections for the specified user.
 * @throws Throws an error if the fetch fails or if the status code is not 200.
 */
export const getCollectionsByUsername = async (
  usernameToView: string,
  currentUsername?: string,
): Promise<PopulatedDatabaseCollection[]> => {
  const queryParam = currentUsername ? `?currentUsername=${currentUsername}` : '';
  const res = await api.get(
    `${COLLECTION_API_URL}/getCollectionsByUsername/${usernameToView}${queryParam}`,
  );

  if (res.status !== 200) {
    throw new Error('Error when fetching collections for user');
  }

  return res.data;
};

/**
 * Fetches a specific collection by its ID.
 *
 * @param collectionId - The ID of the collection to fetch.
 * @param username - The username of the current user.
 * @returns The collection data.
 * @throws Throws an error if the fetch fails or if the status code is not 200.
 */
export const getCollectionById = async (
  collectionId: string,
  username: string,
): Promise<PopulatedDatabaseCollection> => {
  const res = await api.get(
    `${COLLECTION_API_URL}/getCollectionById/${collectionId}?username=${username}`,
  );

  if (res.status !== 200) {
    throw new Error('Error when fetching collection');
  }

  return res.data;
};

/**
 * Creates a new collection.
 *
 * @param collection - The collection data to create.
 * @returns The created collection.
 * @throws Throws an error if the creation fails or if the status code is not 200.
 */
export const createCollection = async (
  collection: Collection,
): Promise<PopulatedDatabaseCollection> => {
  const res = await api.post(`${COLLECTION_API_URL}/create`, collection);

  if (res.status !== 200) {
    throw new Error('Error when creating collection');
  }

  return res.data;
};

/**
 * Deletes a collection by its ID.
 *
 * @param collectionId - The ID of the collection to delete.
 * @throws Throws an error if the deletion fails or if the status code is not 200.
 */
export const deleteCollection = async (collectionId: string): Promise<void> => {
  const res = await api.delete(`${COLLECTION_API_URL}/delete/${collectionId}`);

  if (res.status !== 200) {
    throw new Error('Error when deleting collection');
  }
};

/**
 * Toggles saving a question to a collection.
 *
 * @param collectionId - The ID of the collection.
 * @param questionId - The ID of the question.
 * @param username - The username of the current user.
 * @returns The updated collection.
 * @throws Throws an error if the operation fails or if the status code is not 200.
 */
export const toggleSaveQuestionToCollection = async (
  collectionId: string,
  questionId: string,
  username: string,
): Promise<PopulatedDatabaseCollection> => {
  const res = await api.patch(`${COLLECTION_API_URL}/toggleSaveQuestion`, {
    collectionId,
    questionId,
    username,
  });

  if (res.status !== 200) {
    throw new Error('Error when toggling question save status');
  }

  return res.data;
};
